-- 菜单 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('公司管理', 1, 10, 'company', 'system/company/index', 1, 0, 'C', '0', '0', 'system:company:list', 'building', 'admin', SYSDATE(), '', NULL, '公司管理菜单');

-- 按钮 SQL
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('公司查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:company:query', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('公司新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:company:add', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('公司修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:company:edit', '#', 'admin', SYSDATE(), '', NULL, '');

INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES ('公司删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:company:remove', '#', 'admin', SYSDATE(), '', NULL, '');