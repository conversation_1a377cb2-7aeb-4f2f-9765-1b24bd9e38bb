@import '~ant-design-vue/es/style/themes/default.less';

.text-overflow() {
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  white-space: nowrap;
}

// mixins for clearfix
// ------------------------
.clearfix() {
  zoom: 1;

  &::before,
  &::after {
    display: table;
    content: ' ';
  }

  &::after {
    height: 0;
    clear: both;
    font-size: 0;
    visibility: hidden;
  }
}

.page-header-content {
  display: flex;
  padding: 12px;
  margin-bottom: 24px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;

  .avatar {
    flex: 0 1 72px;

    & > span {
      display: block;
      width: 72px;
      height: 72px;
      border-radius: 72px;
    }
  }

  .content {
    position: relative;
    top: 4px;
    margin-left: 24px;
    line-height: 22px;
    color: rgba(0,0,0,.45);
    flex: 1 1 auto;

    .content-title {
      margin-bottom: 12px;
      font-size: 20px;
      font-weight: 500;
      line-height: 28px;
      color: rgba(0,0,0,.85);
    }
  }
}

.extra-content {
  .clearfix();

  float: right;
  white-space: nowrap;

  .stat-item {
    position: relative;
    display: inline-block;
    padding: 0 32px;

    > p:first-child {
      margin-bottom: 4px;
      font-size: 14px;
      line-height: 22px;
      color: rgba(0,0,0,.45);
    }

    > p {
      margin: 0;
      font-size: 30px;
      line-height: 38px;
      color: rgba(0,0,0,.85);

      > span {
        font-size: 20px;
        color: rgba(0,0,0,.45);
      }
    }

    &::after {
      position: absolute;
      top: 8px;
      right: 0;
      width: 1px;
      height: 40px;
      background-color: #e8e8e8;
      content: '';
    }

    &:last-child {
      padding-right: 0;

      &::after {
        display: none;
      }
    }
  }
}
