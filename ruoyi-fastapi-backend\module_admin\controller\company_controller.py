from datetime import datetime
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Union, Literal
from pydantic_validation_decorator import ValidateFields
from config.get_db import get_db
from config.enums import BusinessType
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_vo import (
    AddCompanyModel,
    CompanyPageQueryModel,
    DeleteCompanyModel,
    EditCompanyModel,
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.company_service import CompanyService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil

companyController = APIRouter(prefix='/system/company', dependencies=[Depends(LoginService.get_current_user)])


@companyController.get('/list', dependencies=[Depends(CheckUserInterfaceAuth('system:company:list'))])
async def get_company_list(
    request: Request,
    company_name: str = None,
    status: str = None,
    begin_time: str = None,
    end_time: str = None,
    page_num: int = 1,
    page_size: int = 10,
    order_by_column: str = 'company_id',
    is_asc: str = 'desc',
    query_db: AsyncSession = Depends(get_db),
):
    """获取公司列表"""
    query_object = {
        "company_name": company_name,
        "status": status,
        "begin_time": begin_time,
        "end_time": end_time,
        "page_num": page_num,
        "page_size": page_size,
        "order_by_column": order_by_column,
        "is_asc": is_asc
    }
    query_object = CommonUtil.remove_none(query_object)
    company_list_result = await CompanyService.get_company_list_services(query_db, query_object)
    return company_list_result


@companyController.post('', dependencies=[Depends(CheckUserInterfaceAuth('system:company:add'))])
@ValidateFields(validate_model='add_company')
@Log(title='公司管理', business_type=BusinessType.INSERT)
async def add_system_company(
    request: Request,
    add_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """新增公司"""
    add_company.create_by = current_user.user.user_name
    add_company.create_time = datetime.now()
    add_company.update_by = current_user.user.user_name
    add_company.update_time = datetime.now()
    add_company_result = await CompanyService.add_company_services(query_db, add_company)
    logger.info(add_company_result.message)

    return ResponseUtil.success(msg=add_company_result.message)


@companyController.put('', dependencies=[Depends(CheckUserInterfaceAuth('system:company:edit'))])
@ValidateFields(validate_model='edit_company')
@Log(title='公司管理', business_type=BusinessType.UPDATE)
async def edit_system_company(
    request: Request,
    edit_company: CompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """修改公司"""
    edit_company.update_by = current_user.user.user_name
    edit_company.update_time = datetime.now()
    edit_company_result = await CompanyService.edit_company_services(query_db, edit_company)
    logger.info(edit_company_result.message)

    return ResponseUtil.success(msg=edit_company_result.message)


@companyController.delete('/{company_ids}', dependencies=[Depends(CheckUserInterfaceAuth('system:company:remove'))])
@Log(title='公司管理', business_type=BusinessType.DELETE)
async def delete_system_company(request: Request, company_ids: str, query_db: AsyncSession = Depends(get_db)):
    """删除公司"""
    delete_company = DeleteCompanyModel(companyIds=company_ids)
    delete_company_result = await CompanyService.delete_company_services(query_db, delete_company)
    logger.info(delete_company_result.message)

    return ResponseUtil.success(msg=delete_company_result.message)


@companyController.get(
    '/{company_id}', response_model=CompanyModel, dependencies=[Depends(CheckUserInterfaceAuth('system:company:query'))]
)
async def get_company_detail(request: Request, company_id: int, query_db: AsyncSession = Depends(get_db)):
    """获取公司详情"""
    company_detail_result = await CompanyService.get_company_detail_services(query_db, company_id)
    return company_detail_result