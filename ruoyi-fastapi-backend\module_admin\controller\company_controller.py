from datetime import datetime
from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Union, Literal
from pydantic_validation_decorator import ValidateFields
from config.get_db import get_db
from config.enums import BusinessType
from module_admin.annotation.log_annotation import Log
from module_admin.aspect.interface_auth import CheckUserInterfaceAuth
from module_admin.entity.vo.company_vo import (
    AddCompanyModel,
    CompanyPageQueryModel,
    DeleteCompanyModel,
    EditCompanyModel,
)
from module_admin.entity.vo.user_vo import CurrentUserModel
from module_admin.service.company_service import CompanyService
from module_admin.service.login_service import LoginService
from utils.log_util import logger
from utils.page_util import PageResponseModel
from utils.response_util import ResponseUtil

companyController = APIRouter(prefix='/system/company', dependencies=[Depends(LoginService.get_current_user)])


@companyController.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('system:company:list'))]
)
async def get_system_company_list(
    request: Request,
    company_page_query: CompanyPageQueryModel = Depends(CompanyPageQueryModel.as_query),
    query_db: AsyncSession = Depends(get_db),
):
    """
    获取公司列表
    """
    # 获取分页数据
    company_page_query_result = await CompanyService.get_company_list_services(
        query_db, company_page_query, is_page=True
    )
    logger.info('获取成功')

    return ResponseUtil.success(model_content=company_page_query_result)


@companyController.post('', dependencies=[Depends(CheckUserInterfaceAuth('system:company:add'))])
@ValidateFields(validate_model='add_company')
@Log(title='公司管理', business_type=BusinessType.INSERT)
async def add_system_company(
    request: Request,
    add_company: AddCompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    新增公司
    """
    add_company_result = await CompanyService.add_company_services(query_db, add_company)
    logger.info('新增成功')

    return ResponseUtil.success(message=add_company_result.message)


@companyController.put('', dependencies=[Depends(CheckUserInterfaceAuth('system:company:edit'))])
@ValidateFields(validate_model='edit_company')
@Log(title='公司管理', business_type=BusinessType.UPDATE)
async def edit_system_company(
    request: Request,
    edit_company: EditCompanyModel,
    query_db: AsyncSession = Depends(get_db),
    current_user: CurrentUserModel = Depends(LoginService.get_current_user),
):
    """
    修改公司
    """
    edit_company_result = await CompanyService.edit_company_services(query_db, edit_company)
    logger.info('更新成功')

    return ResponseUtil.success(message=edit_company_result.message)


@companyController.delete('/{company_ids}', dependencies=[Depends(CheckUserInterfaceAuth('system:company:remove'))])
@Log(title='公司管理', business_type=BusinessType.DELETE)
async def delete_system_company(request: Request, company_ids: str, query_db: AsyncSession = Depends(get_db)):
    """
    删除公司
    """
    delete_company = DeleteCompanyModel(ids=company_ids)
    delete_company_result = await CompanyService.delete_company_services(query_db, delete_company)
    logger.info('删除成功')

    return ResponseUtil.success(message=delete_company_result.message)


@companyController.get('/{company_id}', dependencies=[Depends(CheckUserInterfaceAuth('system:company:query'))])
async def get_system_company_detail(request: Request, company_id: str, query_db: AsyncSession = Depends(get_db)):
    """
    获取公司详情
    """
    company_detail_result = await CompanyService.get_company_detail_services(query_db, company_id)
    logger.info('获取成功')

    return ResponseUtil.success(data=company_detail_result)