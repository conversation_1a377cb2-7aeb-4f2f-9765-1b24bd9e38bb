#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/4/25 16:25
# <AUTHOR> 冉勇
# @Site    : 
# @File    : company_model.py
# @Software: PyCharm
# @desc    : 公司管理模型

from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, Field


class CompanyModel(BaseModel):
    """公司管理模型"""
    company_id: Optional[int] = Field(default=None, description="公司ID")
    company_name: Optional[str] = Field(default=None, description="公司名称")
    address: Optional[str] = Field(default=None, description="公司地址")
    phone: Optional[str] = Field(default=None, description="联系电话")
    email: Optional[str] = Field(default=None, description="电子邮箱")
    status: Optional[str] = Field(default=None, description="公司状态（0正常 1停用）")
    create_by: Optional[str] = Field(default=None, description="创建者")
    create_time: Optional[datetime] = Field(default=None, description="创建时间")
    update_by: Optional[str] = Field(default=None, description="更新者")
    update_time: Optional[datetime] = Field(default=None, description="更新时间")
    remark: Optional[str] = Field(default=None, description="备注")


class DeleteCompanyModel(BaseModel):
    """删除公司模型"""
    companyIds: str = Field(..., description="公司ID字符串，多个以逗号分隔")