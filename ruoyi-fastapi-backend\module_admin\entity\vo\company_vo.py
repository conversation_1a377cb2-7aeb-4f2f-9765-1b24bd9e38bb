from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from pydantic.alias_generators import to_camel
from typing import Optional, Union
from module_admin.annotation.pydantic_annotation import as_query


class CompanyModel(BaseModel):
    """
    公司表对应pydantic模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    id: Optional[str] = Field(default=None, description='公司ID')
    name: Optional[str] = Field(default=None, description='公司名称')
    city: Optional[str] = Field(default=None, description='所在城市')
    address: Optional[str] = Field(default=None, description='公司地址')
    address_desc: Optional[str] = Field(default=None, description='地址描述')
    version_type_name: Optional[str] = Field(default=None, description='版本类型名称')
    status: Optional[str] = Field(default='1', description='状态（0停用 1正常）')
    is_delete: Optional[str] = Field(default='0', description='删除标志（0代表存在 1代表删除）')
    create_time: Optional[Union[datetime, str]] = Field(default=None, description='创建时间')
    update_time: Optional[Union[datetime, str]] = Field(default=None, description='更新时间')


class CompanyQueryModel(CompanyModel):
    """
    公司管理不分页查询模型
    """
    begin_time: Optional[str] = Field(default=None, description='开始时间')
    end_time: Optional[str] = Field(default=None, description='结束时间')


@as_query
class CompanyPageQueryModel(CompanyQueryModel):
    """
    公司管理分页查询模型
    """
    page_num: int = Field(default=1, description='当前页码')
    page_size: int = Field(default=10, description='每页记录数')


class AddCompanyModel(BaseModel):
    """
    新增公司模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    name: str = Field(description='公司名称')
    city: Optional[str] = Field(default=None, description='所在城市')
    address: Optional[str] = Field(default=None, description='公司地址')
    address_desc: Optional[str] = Field(default=None, description='地址描述')
    version_type_name: Optional[str] = Field(default=None, description='版本类型名称')
    status: Optional[str] = Field(default='1', description='状态（0停用 1正常）')


class EditCompanyModel(AddCompanyModel):
    """
    编辑公司模型
    """
    id: str = Field(description='公司ID')


class DeleteCompanyModel(BaseModel):
    """
    删除公司模型
    """
    model_config = ConfigDict(alias_generator=to_camel)

    ids: str = Field(description='需要删除的公司ID')


class CompanyDetailModel(CompanyModel):
    """
    公司详情模型
    """
    pass
