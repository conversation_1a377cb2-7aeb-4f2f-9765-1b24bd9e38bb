import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from exceptions.exception import ServiceException
from module_admin.dao.company_dao import CompanyDao
from module_admin.entity.vo.common_vo import CrudResponseModel
from module_admin.entity.vo.company_vo import (
    AddCompanyModel,
    CompanyDetailModel,
    CompanyModel,
    CompanyPageQueryModel,
    DeleteCompanyModel,
    EditCompanyModel,
)
from utils.page_util import PageResponseModel


class CompanyService:
    """
    公司管理模块服务层
    """

    @classmethod
    async def get_company_list_services(
        cls, query_db: AsyncSession, query_object: CompanyPageQueryModel, is_page: bool = False
    ):
        """
        获取公司列表信息service

        :param query_db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司列表信息对象
        """
        query_result = await CompanyDao.get_company_list(query_db, query_object, is_page)
        if is_page:
            company_list_result = PageResponseModel(
                **{
                    **query_result.model_dump(by_alias=True),
                    'rows': [row.__dict__ for row in query_result.rows],
                }
            )
        else:
            company_list_result = []
            if query_result:
                company_list_result = [row.__dict__ for row in query_result]

        return company_list_result

    @classmethod
    async def get_company_detail_services(cls, query_db: AsyncSession, company_id: str):
        """
        获取公司详情信息service

        :param query_db: orm对象
        :param company_id: 公司id
        :return: 公司id对应的信息
        """
        company_info = await CompanyDao.get_company_detail_by_id(query_db, company_id)
        if company_info:
            result = CompanyDetailModel(**company_info.__dict__)
        else:
            result = CompanyDetailModel(**{})

        return result

    @classmethod
    async def add_company_services(cls, query_db: AsyncSession, page_object: AddCompanyModel):
        """
        新增公司信息service

        :param query_db: orm对象
        :param page_object: 新增公司对象
        :return: 新增公司校验结果
        """
        add_company = CompanyModel(
            id=str(uuid.uuid4()),
            **page_object.model_dump(by_alias=True),
            create_time=datetime.now(),
            update_time=datetime.now()
        )
        if not await cls.check_company_name_unique_services(query_db, add_company):
            raise ServiceException(message=f'新增公司{page_object.name}失败，公司名称已存在')
        else:
            try:
                await CompanyDao.add_company_dao(query_db, add_company)
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='新增成功')
            except Exception as e:
                await query_db.rollback()
                raise e

    @classmethod
    async def edit_company_services(cls, query_db: AsyncSession, page_object: EditCompanyModel):
        """
        编辑公司信息service

        :param query_db: orm对象
        :param page_object: 编辑公司对象
        :return: 编辑公司校验结果
        """
        edit_company = page_object.model_copy(update={'update_time': datetime.now()})
        company_info = await CompanyDao.get_company_detail_by_id(query_db, edit_company.id)
        if company_info:
            if not await cls.check_company_name_unique_services(query_db, edit_company):
                raise ServiceException(message=f'修改公司{page_object.name}失败，公司名称已存在')
            else:
                try:
                    await CompanyDao.edit_company_dao(query_db, edit_company)
                    await query_db.commit()
                    return CrudResponseModel(is_success=True, message='更新成功')
                except Exception as e:
                    await query_db.rollback()
                    raise e
        else:
            raise ServiceException(message='公司不存在')

    @classmethod
    async def delete_company_services(cls, query_db: AsyncSession, page_object: DeleteCompanyModel):
        """
        删除公司信息service

        :param query_db: orm对象
        :param page_object: 删除公司对象
        :return: 删除公司校验结果
        """
        if page_object.ids:
            company_id_list = page_object.ids.split(',')
            try:
                for company_id in company_id_list:
                    company_info = await CompanyDao.get_company_detail_by_id(query_db, company_id)
                    if company_info:
                        await CompanyDao.delete_company_dao(query_db, CompanyModel(id=company_id))
                await query_db.commit()
                return CrudResponseModel(is_success=True, message='删除成功')
            except Exception as e:
                await query_db.rollback()
                raise e
        else:
            raise ServiceException(message='传入公司id为空')

    @classmethod
    async def check_company_name_unique_services(cls, query_db: AsyncSession, page_object: CompanyModel):
        """
        校验公司名称是否唯一service

        :param query_db: orm对象
        :param page_object: 公司对象
        :return: 校验结果
        """
        company_id = page_object.id
        company_name = page_object.name
        company_info = await CompanyDao.get_company_detail_by_info(
            query_db, CompanyModel(id=company_id, name=company_name)
        )
        if company_info:
            return False
        else:
            return True