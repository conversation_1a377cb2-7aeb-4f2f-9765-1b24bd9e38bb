#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/4/25 16:35
# <AUTHOR> 冉勇
# @Site    : 
# @File    : company_service.py
# @Software: PyCharm
# @desc    : 公司管理服务

from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.dao.company_dao import CompanyDao
from module_admin.model.company_model import CompanyModel, DeleteCompanyModel
from utils.common_util import CommonUtil
from utils.response_util import ResponseUtil


class CompanyService:
    """公司管理服务"""

    @classmethod
    async def get_company_list_services(cls, db: AsyncSession, query_object: dict):
        """
        获取公司列表服务

        :param db: orm对象
        :param query_object: 查询参数
        :return:
        """
        company_list_result = await CompanyDao.get_company_list(db, query_object)
        return company_list_result

    @classmethod
    async def get_company_detail_services(cls, db: AsyncSession, company_id: int):
        """
        获取公司详情服务

        :param db: orm对象
        :param company_id: 公司ID
        :return:
        """
        company_info = await CompanyDao.get_company_by_id(db, company_id)
        if not company_info:
            return ResponseUtil.business_error("未找到该公司信息")
        return ResponseUtil.success(data=company_info)

    @classmethod
    async def add_company_services(cls, db: AsyncSession, company: CompanyModel):
        """
        新增公司服务

        :param db: orm对象
        :param company: 公司对象
        :return:
        """
        try:
            await CompanyDao.add_company_dao(db, company)
            await db.commit()
            return ResponseUtil.success(msg="新增公司成功")
        except Exception as e:
            await db.rollback()
            return ResponseUtil.error(msg=f"新增公司失败：{str(e)}")

    @classmethod
    async def edit_company_services(cls, db: AsyncSession, company: CompanyModel):
        """
        编辑公司服务

        :param db: orm对象
        :param company: 公司对象
        :return:
        """
        try:
            company_info = await CompanyDao.get_company_by_id(db, company.company_id)
            if not company_info:
                return ResponseUtil.business_error("未找到该公司信息")
            
            company_dict = company.model_dump(exclude_unset=True)
            company_dict = CommonUtil.remove_none(company_dict)
            
            await CompanyDao.edit_company_dao(db, company_dict)
            await db.commit()
            return ResponseUtil.success(msg="修改公司成功")
        except Exception as e:
            await db.rollback()
            return ResponseUtil.error(msg=f"修改公司失败：{str(e)}")

    @classmethod
    async def delete_company_services(cls, db: AsyncSession, delete_company: DeleteCompanyModel):
        """
        删除公司服务

        :param db: orm对象
        :param delete_company: 删除公司对象
        :return:
        """
        try:
            company_ids = delete_company.companyIds.split(",")
            for company_id in company_ids:
                company = CompanyModel(company_id=int(company_id))
                await CompanyDao.delete_company_dao(db, company)
            await db.commit()
            return ResponseUtil.success(msg="删除公司成功")
        except Exception as e:
            await db.rollback()
            return ResponseUtil.error(msg=f"删除公司失败：{str(e)}")