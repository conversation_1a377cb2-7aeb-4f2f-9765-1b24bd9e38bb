#!/usr/bin/env python
# -*- coding: utf-8 -*-
# @Time    : 2023/4/25 16:30
# <AUTHOR> 冉勇
# @Site    : 
# @File    : company_dao.py
# @Software: PyCharm
# @desc    : 公司管理数据库操作

from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession

from module_admin.entity.sys_company import SysCompany
from module_admin.model.company_model import CompanyModel
from utils.page_util import PageUtil


class CompanyDao:
    """公司管理数据库操作"""

    @classmethod
    async def get_company_list(cls, db: AsyncSession, query_db: dict):
        """
        获取公司列表数据库操作

        :param db: orm对象
        :param query_db: 查询参数
        :return:
        """
        company_name = query_db.get("company_name")
        status = query_db.get("status")
        begin_time = query_db.get("begin_time")
        end_time = query_db.get("end_time")
        page_num = query_db.get("page_num")
        page_size = query_db.get("page_size")
        order_by_column = query_db.get("order_by_column") if query_db.get("order_by_column") else "company_id"
        is_asc = query_db.get("is_asc") if query_db.get("is_asc") else "desc"

        if order_by_column == 'createTime':
            order_by_column = 'create_time'

        sql = select(SysCompany)
        where_list = []
        if company_name:
            where_list.append(SysCompany.company_name.like(f"%{company_name}%"))
        if status:
            where_list.append(SysCompany.status == status)
        if begin_time and end_time:
            where_list.append(SysCompany.create_time.between(begin_time, end_time))
        if where_list:
            sql = sql.where(*where_list)

        count_sql = select(func.count(SysCompany.company_id)).select_from(SysCompany)
        if where_list:
            count_sql = count_sql.where(*where_list)

        if is_asc.lower() == "desc":
            sql = sql.order_by(getattr(SysCompany, order_by_column).desc())
        else:
            sql = sql.order_by(getattr(SysCompany, order_by_column).asc())

        result = await PageUtil.paging(db, sql, count_sql, page_num, page_size)
        return result

    @classmethod
    async def get_company_by_id(cls, db: AsyncSession, company_id: int):
        """
        根据ID获取公司信息数据库操作

        :param db: orm对象
        :param company_id: 公司ID
        :return:
        """
        sql = select(SysCompany).where(SysCompany.company_id == company_id)
        result = await db.execute(sql)
        return result.scalars().first()

    @classmethod
    async def add_company_dao(cls, db: AsyncSession, company: CompanyModel):
        """
        新增公司数据库操作

        :param db: orm对象
        :param company: 公司对象
        :return:
        """
        db_company = SysCompany(**company.model_dump())
        db.add(db_company)
        await db.flush()

        return db_company

    @classmethod
    async def edit_company_dao(cls, db: AsyncSession, company: dict):
        """
        编辑公司数据库操作

        :param db: orm对象
        :param company: 需要更新的公司字典
        :return:
        """
        await db.execute(update(SysCompany), [company])

    @classmethod
    async def delete_company_dao(cls, db: AsyncSession, company: CompanyModel):
        """
        删除公司数据库操作

        :param db: orm对象
        :param company: 公司对象
        :return:
        """
        await db.execute(delete(SysCompany).where(SysCompany.company_id.in_([company.company_id])))