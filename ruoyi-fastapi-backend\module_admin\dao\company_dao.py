from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from module_admin.entity.do.company_do import Company
from module_admin.entity.vo.company_vo import CompanyModel, CompanyPageQueryModel
from utils.page_util import PageUtil


class CompanyDao:
    """
    公司管理模块数据库操作层
    """

    @classmethod
    async def get_company_list(
        cls, db: AsyncSession, query_object: CompanyPageQueryModel, is_page: bool = False
    ):
        """
        根据查询参数获取公司列表信息

        :param db: orm对象
        :param query_object: 查询参数对象
        :param is_page: 是否开启分页
        :return: 公司列表信息对象
        """
        query = (
            select(Company)
            .where(
                Company.is_delete == '0',
                Company.name.like(f'%{query_object.name}%') if query_object.name else True,
                Company.city.like(f'%{query_object.city}%') if query_object.city else True,
                Company.status == query_object.status if query_object.status else True,
                Company.version_type_name.like(f'%{query_object.version_type_name}%')
                if query_object.version_type_name else True,
                Company.create_time.between(
                    datetime.combine(datetime.strptime(query_object.begin_time, '%Y-%m-%d'), time(0, 0, 0)),
                    datetime.combine(datetime.strptime(query_object.end_time, '%Y-%m-%d'), time(23, 59, 59)),
                )
                if query_object.begin_time and query_object.end_time
                else True,
            )
            .order_by(desc(Company.create_time))
            .distinct()
        )
        company_list = await PageUtil.paginate(db, query, query_object.page_num, query_object.page_size, is_page)

        return company_list

    @classmethod
    async def get_company_detail_by_id(cls, db: AsyncSession, company_id: str):
        """
        根据公司id获取公司详细信息

        :param db: orm对象
        :param company_id: 公司id
        :return: 当前公司id的公司信息对象
        """
        query_company_info = (
            (
                await db.execute(
                    select(Company)
                    .where(Company.is_delete == '0', Company.id == company_id)
                    .distinct()
                )
            )
            .scalars()
            .first()
        )

        return query_company_info

    @classmethod
    async def add_company_dao(cls, db: AsyncSession, company: CompanyModel):
        """
        新增公司数据库操作

        :param db: orm对象
        :param company: 公司对象
        :return: 新增校验结果
        """
        db_company = Company(**company.model_dump())
        db.add(db_company)
        await db.flush()

        return db_company

    @classmethod
    async def edit_company_dao(cls, db: AsyncSession, company: CompanyModel):
        """
        编辑公司数据库操作

        :param db: orm对象
        :param company: 需要更新的公司对象
        :return: 编辑校验结果
        """
        await db.execute(
            update(Company)
            .where(Company.id == company.id)
            .values(**company.model_dump(exclude_unset=True, exclude={'id'}))
        )

    @classmethod
    async def delete_company_dao(cls, db: AsyncSession, company: CompanyModel):
        """
        删除公司数据库操作

        :param db: orm对象
        :param company: 需要删除的公司对象
        :return: 删除校验结果
        """
        await db.execute(
            update(Company)
            .where(Company.id == company.id)
            .values(is_delete='1', update_time=datetime.now())
        )

    @classmethod
    async def get_company_detail_by_info(cls, db: AsyncSession, company: CompanyModel):
        """
        根据公司参数获取公司信息

        :param db: orm对象
        :param company: 公司参数对象
        :return: 公司信息对象
        """
        query_company = (
            await db.execute(
                select(Company)
                .where(
                    Company.is_delete == '0',
                    Company.name == company.name if company.name else True,
                    Company.id != company.id if company.id else True,
                )
                .order_by(desc(Company.create_time))
                .distinct()
            )
        ).scalars().first()

        return query_company