from sqlalchemy import Column, String, DateTime
from sqlalchemy.sql import func
from config.database import Base


class Company(Base):
    """
    公司信息表 company
    """

    __tablename__ = 'company'

    id = Column(String(64), primary_key=True, comment='公司ID')
    name = Column(String(100), nullable=False, comment='公司名称')
    city = Column(String(50), comment='所在城市')
    address = Column(String(255), comment='公司地址')
    address_desc = Column(String(255), comment='地址描述')
    version_type_name = Column(String(50), comment='版本类型名称')
    status = Column(String(2), default='1', comment='状态（0停用 1正常）')
    is_delete = Column(String(2), default='0', comment='删除标志（0代表存在 1代表删除）')
    create_time = Column(DateTime, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
